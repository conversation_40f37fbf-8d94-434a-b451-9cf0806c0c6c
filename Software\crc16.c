

const unsigned short u16CrcTalbeAbs[]=
{
    0x0000, 0xCC01, 0xD801, 0x1400, 0xF001, 0x3C00,
    0x2800, 0xE401, 0xA001, 0x6C00, 0x7800, 0xB401,
    0x5000, 0x9C01, 0x8801, 0x4400,
};

unsigned short Crc16(unsigned char *pchMsg,unsigned char wDataLen)
{
    unsigned short wCRC = 0xffff;
	unsigned char  chChar;
	
	while(wDataLen--)
	{
		chChar = *pchMsg++;
		wCRC = u16CrcTalbeAbs[(chChar ^ wCRC) & 15] ^ (wCRC >> 4);
		wCRC = u16CrcTalbeAbs[((chChar >> 4) ^ wCRC) & 15] ^ (wCRC >> 4);
	}
	return wCRC;
}






