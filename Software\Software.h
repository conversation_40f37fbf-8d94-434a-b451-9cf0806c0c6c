#ifndef __Software_H
#define __Software_H

#include "Hardware.h"

#define USART1_SEND_BUFFER_SIZE     10
#define USART1_RECEIVE_BUFFER_SIZE  10

#define USART2_SEND_BUFFER_SIZE     10
#define USART2_RECEIVE_BUFFER_SIZE  10

typedef struct
{
	uint8_t sendBuffer[USART1_SEND_BUFFER_SIZE]; 
	uint8_t receiveBuffer[USART1_RECEIVE_BUFFER_SIZE]; 
	
}usart1_management_t;

extern usart1_management_t usart1_management;

typedef struct
{
	uint8_t sendBuffer[USART2_SEND_BUFFER_SIZE]; 
	uint8_t receiveBuffer[USART2_RECEIVE_BUFFER_SIZE]; 
	
}usart2_management_t;

extern usart2_management_t usart2_management;

typedef struct
{
	uint8_t concentration[8]; 	
}data_management_t;

extern data_management_t data_management;

typedef struct
{
	uint8_t count;
	uint8_t flag_100ms;
	uint8_t flag_900ms;
	uint8_t flag_1s;
}timer2_management_t;

extern timer2_management_t timer2_management;

void software_initializes(void);


#endif

