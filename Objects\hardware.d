.\objects\hardware.o: Hardware\Hardware.c
.\objects\hardware.o: Hardware\Hardware.h
.\objects\hardware.o: .\Start\stm32f0xx.h
.\objects\hardware.o: .\Start\core_cm0.h
.\objects\hardware.o: D:\Keli\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\hardware.o: .\Start\core_cmInstr.h
.\objects\hardware.o: .\Start\core_cmFunc.h
.\objects\hardware.o: .\Start\system_stm32f0xx.h
.\objects\hardware.o: .\Start\stm32f0xx_conf.h
.\objects\hardware.o: .\Library\stm32f0xx_adc.h
.\objects\hardware.o: .\Start\stm32f0xx.h
.\objects\hardware.o: .\Library\stm32f0xx_can.h
.\objects\hardware.o: .\Library\stm32f0xx_cec.h
.\objects\hardware.o: .\Library\stm32f0xx_crc.h
.\objects\hardware.o: .\Library\stm32f0xx_crs.h
.\objects\hardware.o: .\Library\stm32f0xx_comp.h
.\objects\hardware.o: .\Library\stm32f0xx_dac.h
.\objects\hardware.o: .\Library\stm32f0xx_dbgmcu.h
.\objects\hardware.o: .\Library\stm32f0xx_dma.h
.\objects\hardware.o: .\Library\stm32f0xx_exti.h
.\objects\hardware.o: .\Library\stm32f0xx_flash.h
.\objects\hardware.o: .\Library\stm32f0xx_gpio.h
.\objects\hardware.o: .\Library\stm32f0xx_syscfg.h
.\objects\hardware.o: .\Library\stm32f0xx_i2c.h
.\objects\hardware.o: .\Library\stm32f0xx_iwdg.h
.\objects\hardware.o: .\Library\stm32f0xx_pwr.h
.\objects\hardware.o: .\Library\stm32f0xx_rcc.h
.\objects\hardware.o: .\Library\stm32f0xx_rtc.h
.\objects\hardware.o: .\Library\stm32f0xx_spi.h
.\objects\hardware.o: .\Library\stm32f0xx_tim.h
.\objects\hardware.o: .\Library\stm32f0xx_usart.h
.\objects\hardware.o: .\Library\stm32f0xx_wwdg.h
.\objects\hardware.o: .\Library\stm32f0xx_misc.h
.\objects\hardware.o: D:\Keli\Keil_v5\ARM\ARMCC\Bin\..\include\string.h
.\objects\hardware.o: Hardware\Delay.h
.\objects\hardware.o: Hardware\Hardware.h
.\objects\hardware.o: Hardware\Can.h
.\objects\hardware.o: Hardware\Timer.h
.\objects\hardware.o: .\Software\Software.h
.\objects\hardware.o: .\Software\crc16.h
