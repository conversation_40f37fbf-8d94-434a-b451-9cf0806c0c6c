


ARM Macro Assembler    Page 1 


    1 00000000         ;******************** (C) COPYRIGHT 2016 STMicroelectron
                       ics ********************
    2 00000000         ;* File Name          : startup_stm32f091.s
    3 00000000         ;* Author             : MCD Application Team
    4 00000000         ;* Version            : V1.5.1
    5 00000000         ;* Date               : 13-October-2021
    6 00000000         ;* Description        : STM32F091 Devices vector table f
                       or
    7 00000000         ;*                      for MDK-ARM toolchain.
    8 00000000         ;*                      This module performs:
    9 00000000         ;*                      - Set the initial SP
   10 00000000         ;*                      - Set the initial PC == Reset_Ha
                       ndler
   11 00000000         ;*                      - Set the vector table entries w
                       ith the exceptions ISR address
   12 00000000         ;*                      - Configure the system clock
   13 00000000         ;*                      - Branches to __main in the C li
                       brary (which eventually
   14 00000000         ;*                        calls main()).
   15 00000000         ;*                      After Reset the CortexM0 process
                       or is in Thread mode,
   16 00000000         ;*                      priority is Privileged, and the 
                       Stack is set to Main.
   17 00000000         ;* <<< Use Configuration Wizard in Context Menu >>>   
   18 00000000         ;*******************************************************
                       ************************
   19 00000000         ;*
   20 00000000         ;* Copyright (c) 2016 STMicroelectronics.
   21 00000000         ;* All rights reserved.
   22 00000000         ;*
   23 00000000         ;* This software is licensed under terms that can be fou
                       nd in the LICENSE file
   24 00000000         ;* in the root directory of this software component.
   25 00000000         ;* If no LICENSE file comes with this software, it is pr
                       ovided AS-IS.
   26 00000000         ;
   27 00000000         ;*******************************************************
                       ************************
   28 00000000         ;
   29 00000000         ; Amount of memory (in bytes) allocated for Stack
   30 00000000         ; Tailor this value to your application needs
   31 00000000         ; <h> Stack Configuration
   32 00000000         ;   <o> Stack Size (in Bytes) <0x0-0xFFFFFFFF:8>
   33 00000000         ; </h>
   34 00000000         
   35 00000000 00000400 
                       Stack_Size
                               EQU              0x00000400
   36 00000000         
   37 00000000                 AREA             STACK, NOINIT, READWRITE, ALIGN
=3
   38 00000000         Stack_Mem
                               SPACE            Stack_Size
   39 00000400         __initial_sp
   40 00000400         
   41 00000400         
   42 00000400         ; <h> Heap Configuration
   43 00000400         ;   <o>  Heap Size (in Bytes) <0x0-0xFFFFFFFF:8>
   44 00000400         ; </h>



ARM Macro Assembler    Page 2 


   45 00000400         
   46 00000400 00000200 
                       Heap_Size
                               EQU              0x00000200
   47 00000400         
   48 00000400                 AREA             HEAP, NOINIT, READWRITE, ALIGN=
3
   49 00000000         __heap_base
   50 00000000         Heap_Mem
                               SPACE            Heap_Size
   51 00000200         __heap_limit
   52 00000200         
   53 00000200                 PRESERVE8
   54 00000200                 THUMB
   55 00000200         
   56 00000200         
   57 00000200         ; Vector Table Mapped to Address 0 at Reset
   58 00000200                 AREA             RESET, DATA, READONLY
   59 00000000                 EXPORT           __Vectors
   60 00000000                 EXPORT           __Vectors_End
   61 00000000                 EXPORT           __Vectors_Size
   62 00000000         
   63 00000000 00000000 
                       __Vectors
                               DCD              __initial_sp ; Top of Stack
   64 00000004 00000000        DCD              Reset_Handler ; Reset Handler
   65 00000008 00000000        DCD              NMI_Handler ; NMI Handler
   66 0000000C 00000000        DCD              HardFault_Handler ; Hard Fault 
                                                            Handler
   67 00000010 00000000        DCD              0           ; Reserved
   68 00000014 00000000        DCD              0           ; Reserved
   69 00000018 00000000        DCD              0           ; Reserved
   70 0000001C 00000000        DCD              0           ; Reserved
   71 00000020 00000000        DCD              0           ; Reserved
   72 00000024 00000000        DCD              0           ; Reserved
   73 00000028 00000000        DCD              0           ; Reserved
   74 0000002C 00000000        DCD              SVC_Handler ; SVCall Handler
   75 00000030 00000000        DCD              0           ; Reserved
   76 00000034 00000000        DCD              0           ; Reserved
   77 00000038 00000000        DCD              PendSV_Handler ; PendSV Handler
                                                            
   78 0000003C 00000000        DCD              SysTick_Handler 
                                                            ; SysTick Handler
   79 00000040         
   80 00000040         ; External Interrupts
   81 00000040 00000000        DCD              WWDG_IRQHandler 
                                                            ; Window Watchdog
   82 00000044 00000000        DCD              PVD_VDDIO2_IRQHandler ; PVD and
                                                             VDDIO2 through EXT
                                                            I Line detect
   83 00000048 00000000        DCD              RTC_IRQHandler ; RTC through EX
                                                            TI Line
   84 0000004C 00000000        DCD              FLASH_IRQHandler ; FLASH
   85 00000050 00000000        DCD              RCC_CRS_IRQHandler 
                                                            ; RCC and CRS
   86 00000054 00000000        DCD              EXTI0_1_IRQHandler 
                                                            ; EXTI Line 0 and 1
                                                            
   87 00000058 00000000        DCD              EXTI2_3_IRQHandler 



ARM Macro Assembler    Page 3 


                                                            ; EXTI Line 2 and 3
                                                            
   88 0000005C 00000000        DCD              EXTI4_15_IRQHandler 
                                                            ; EXTI Line 4 to 15
                                                            
   89 00000060 00000000        DCD              TSC_IRQHandler ; TS
   90 00000064 00000000        DCD              DMA1_Ch1_IRQHandler 
                                                            ; DMA1 Channel 1
   91 00000068 00000000        DCD              DMA1_Ch2_3_DMA2_Ch1_2_IRQHandle
r 
                                                            ; DMA1 Channel 2 an
                                                            d 3 & DMA2 Channel 
                                                            1 and 2
   92 0000006C 00000000        DCD              DMA1_Ch4_7_DMA2_Ch3_5_IRQHandle
r 
                                                            ; DMA1 Channel 4 to
                                                             7 & DMA2 Channel 3
                                                             to 5 
   93 00000070 00000000        DCD              ADC1_COMP_IRQHandler ; ADC1, CO
                                                            MP1 and COMP2 
   94 00000074 00000000        DCD              TIM1_BRK_UP_TRG_COM_IRQHandler 
                                                            ; TIM1 Break, Updat
                                                            e, Trigger and Comm
                                                            utation
   95 00000078 00000000        DCD              TIM1_CC_IRQHandler ; TIM1 Captu
                                                            re Compare
   96 0000007C 00000000        DCD              TIM2_IRQHandler ; TIM2
   97 00000080 00000000        DCD              TIM3_IRQHandler ; TIM3
   98 00000084 00000000        DCD              TIM6_DAC_IRQHandler 
                                                            ; TIM6 and DAC
   99 00000088 00000000        DCD              TIM7_IRQHandler ; TIM7
  100 0000008C 00000000        DCD              TIM14_IRQHandler ; TIM14
  101 00000090 00000000        DCD              TIM15_IRQHandler ; TIM15
  102 00000094 00000000        DCD              TIM16_IRQHandler ; TIM16
  103 00000098 00000000        DCD              TIM17_IRQHandler ; TIM17
  104 0000009C 00000000        DCD              I2C1_IRQHandler ; I2C1
  105 000000A0 00000000        DCD              I2C2_IRQHandler ; I2C2
  106 000000A4 00000000        DCD              SPI1_IRQHandler ; SPI1
  107 000000A8 00000000        DCD              SPI2_IRQHandler ; SPI2
  108 000000AC 00000000        DCD              USART1_IRQHandler ; USART1
  109 000000B0 00000000        DCD              USART2_IRQHandler ; USART2
  110 000000B4 00000000        DCD              USART3_8_IRQHandler ; USART3, U
                                                            SART4, USART5, USAR
                                                            T6, USART7, USART8
  111 000000B8 00000000        DCD              CEC_CAN_IRQHandler 
                                                            ; CEC and CAN
  112 000000BC         
  113 000000BC         __Vectors_End
  114 000000BC         
  115 000000BC 000000BC 
                       __Vectors_Size
                               EQU              __Vectors_End - __Vectors
  116 000000BC         
  117 000000BC                 AREA             |.text|, CODE, READONLY
  118 00000000         
  119 00000000         ; Reset handler routine
  120 00000000         Reset_Handler
                               PROC
  121 00000000                 EXPORT           Reset_Handler                 [



ARM Macro Assembler    Page 4 


WEAK]
  122 00000000                 IMPORT           __main
  123 00000000                 IMPORT           SystemInit
  124 00000000         
  125 00000000         
  126 00000000         
  127 00000000 480F            LDR              R0, =__initial_sp ; set stack p
                                                            ointer 
  128 00000002 F380 8808       MSR              MSP, R0
  129 00000006         
  130 00000006         ;;Check if boot space corresponds to test memory 
  131 00000006         
  132 00000006 480F            LDR              R0,=0x00000004
  133 00000008 6801            LDR              R1, [R0]
  134 0000000A 0E09            LSRS             R1, R1, #24
  135 0000000C 4A0E            LDR              R2,=0x1F
  136 0000000E 4291            CMP              R1, R2
  137 00000010         
  138 00000010 D105            BNE              ApplicationStart
  139 00000012         
  140 00000012         ;; SYSCFG clock enable    
  141 00000012         
  142 00000012 480E            LDR              R0,=0x40021018
  143 00000014 490E            LDR              R1,=0x00000001
  144 00000016 6001            STR              R1, [R0]
  145 00000018         
  146 00000018         ;; Set CFGR1 register with flash memory remap at address
                        0
  147 00000018         
  148 00000018 480E            LDR              R0,=0x40010000
  149 0000001A 490F            LDR              R1,=0x00000000
  150 0000001C 6001            STR              R1, [R0]
  151 0000001E         ApplicationStart
  152 0000001E 480F            LDR              R0, =SystemInit
  153 00000020 4780            BLX              R0
  154 00000022 480F            LDR              R0, =__main
  155 00000024 4700            BX               R0
  156 00000026                 ENDP
  157 00000026         
  158 00000026         ; Dummy Exception Handlers (infinite loops which can be 
                       modified)
  159 00000026         
  160 00000026         NMI_Handler
                               PROC
  161 00000026                 EXPORT           NMI_Handler                    
[WEAK]
  162 00000026 E7FE            B                .
  163 00000028                 ENDP
  165 00000028         HardFault_Handler
                               PROC
  166 00000028                 EXPORT           HardFault_Handler              
[WEAK]
  167 00000028 E7FE            B                .
  168 0000002A                 ENDP
  169 0000002A         SVC_Handler
                               PROC
  170 0000002A                 EXPORT           SVC_Handler                    
[WEAK]
  171 0000002A E7FE            B                .



ARM Macro Assembler    Page 5 


  172 0000002C                 ENDP
  173 0000002C         PendSV_Handler
                               PROC
  174 0000002C                 EXPORT           PendSV_Handler                 
[WEAK]
  175 0000002C E7FE            B                .
  176 0000002E                 ENDP
  177 0000002E         SysTick_Handler
                               PROC
  178 0000002E                 EXPORT           SysTick_Handler                
[WEAK]
  179 0000002E E7FE            B                .
  180 00000030                 ENDP
  181 00000030         
  182 00000030         Default_Handler
                               PROC
  183 00000030         
  184 00000030                 EXPORT           WWDG_IRQHandler                
[WEAK]
  185 00000030                 EXPORT           PVD_VDDIO2_IRQHandler          
[WEAK]
  186 00000030                 EXPORT           RTC_IRQHandler                 
[WEAK]
  187 00000030                 EXPORT           FLASH_IRQHandler               
[WEAK]
  188 00000030                 EXPORT           RCC_CRS_IRQHandler             
[WEAK]
  189 00000030                 EXPORT           EXTI0_1_IRQHandler             
[WEAK]
  190 00000030                 EXPORT           EXTI2_3_IRQHandler             
[WEAK]
  191 00000030                 EXPORT           EXTI4_15_IRQHandler            
[WEAK]
  192 00000030                 EXPORT           TSC_IRQHandler                 
 [WEAK]
  193 00000030                 EXPORT           DMA1_Ch1_IRQHandler            
  [WEAK]
  194 00000030                 EXPORT           DMA1_Ch2_3_DMA2_Ch1_2_IRQHandle
r [WEAK]
  195 00000030                 EXPORT           DMA1_Ch4_7_DMA2_Ch3_5_IRQHandle
r [WEAK]
  196 00000030                 EXPORT           ADC1_COMP_IRQHandler           
  [WEAK]
  197 00000030                 EXPORT           TIM1_BRK_UP_TRG_COM_IRQHandler 
  [WEAK]
  198 00000030                 EXPORT           TIM1_CC_IRQHandler             
  [WEAK]
  199 00000030                 EXPORT           TIM2_IRQHandler                
  [WEAK]
  200 00000030                 EXPORT           TIM3_IRQHandler                
  [WEAK]
  201 00000030                 EXPORT           TIM6_DAC_IRQHandler            
  [WEAK]
  202 00000030                 EXPORT           TIM7_IRQHandler                
  [WEAK]
  203 00000030                 EXPORT           TIM14_IRQHandler               
  [WEAK]
  204 00000030                 EXPORT           TIM15_IRQHandler               
  [WEAK]



ARM Macro Assembler    Page 6 


  205 00000030                 EXPORT           TIM16_IRQHandler               
  [WEAK]
  206 00000030                 EXPORT           TIM17_IRQHandler               
  [WEAK]
  207 00000030                 EXPORT           I2C1_IRQHandler                
  [WEAK]
  208 00000030                 EXPORT           I2C2_IRQHandler                
  [WEAK]
  209 00000030                 EXPORT           SPI1_IRQHandler                
[WEAK]
  210 00000030                 EXPORT           SPI2_IRQHandler                
[WEAK]
  211 00000030                 EXPORT           USART1_IRQHandler              
[WEAK]
  212 00000030                 EXPORT           USART2_IRQHandler              
[WEAK]
  213 00000030                 EXPORT           USART3_8_IRQHandler            
[WEAK]
  214 00000030                 EXPORT           CEC_CAN_IRQHandler             
[WEAK]
  215 00000030         
  216 00000030         
  217 00000030         WWDG_IRQHandler
  218 00000030         PVD_VDDIO2_IRQHandler
  219 00000030         RTC_IRQHandler
  220 00000030         FLASH_IRQHandler
  221 00000030         RCC_CRS_IRQHandler
  222 00000030         EXTI0_1_IRQHandler
  223 00000030         EXTI2_3_IRQHandler
  224 00000030         EXTI4_15_IRQHandler
  225 00000030         TSC_IRQHandler
  226 00000030         DMA1_Ch1_IRQHandler
  227 00000030         DMA1_Ch2_3_DMA2_Ch1_2_IRQHandler
  228 00000030         DMA1_Ch4_7_DMA2_Ch3_5_IRQHandler
  229 00000030         ADC1_COMP_IRQHandler
  230 00000030         TIM1_BRK_UP_TRG_COM_IRQHandler
  231 00000030         TIM1_CC_IRQHandler
  232 00000030         TIM2_IRQHandler
  233 00000030         TIM3_IRQHandler
  234 00000030         TIM6_DAC_IRQHandler
  235 00000030         TIM7_IRQHandler
  236 00000030         TIM14_IRQHandler
  237 00000030         TIM15_IRQHandler
  238 00000030         TIM16_IRQHandler
  239 00000030         TIM17_IRQHandler
  240 00000030         I2C1_IRQHandler
  241 00000030         I2C2_IRQHandler
  242 00000030         SPI1_IRQHandler
  243 00000030         SPI2_IRQHandler
  244 00000030         USART1_IRQHandler
  245 00000030         USART2_IRQHandler
  246 00000030         USART3_8_IRQHandler
  247 00000030         CEC_CAN_IRQHandler
  248 00000030         
  249 00000030 E7FE            B                .
  250 00000032         
  251 00000032                 ENDP
  252 00000032         
  253 00000032 00 00           ALIGN



ARM Macro Assembler    Page 7 


  254 00000034         
  255 00000034         ;*******************************************************
                       ************************
  256 00000034         ; User Stack and Heap initialization
  257 00000034         ;*******************************************************
                       ************************
  258 00000034                 IF               :DEF:__MICROLIB
  265 00000034         
  266 00000034                 IMPORT           __use_two_region_memory
  267 00000034                 EXPORT           __user_initial_stackheap
  268 00000034         
  269 00000034         __user_initial_stackheap
  270 00000034         
  271 00000034 480B            LDR              R0, =  Heap_Mem
  272 00000036 490C            LDR              R1, =(Stack_Mem + Stack_Size)
  273 00000038 4A0C            LDR              R2, = (Heap_Mem +  Heap_Size)
  274 0000003A 4B0D            LDR              R3, = Stack_Mem
  275 0000003C 4770            BX               LR
  276 0000003E         
  277 0000003E 00 00           ALIGN
  278 00000040         
  279 00000040                 ENDIF
  280 00000040         
  281 00000040                 END
              00000000 
              00000004 
              0000001F 
              40021018 
              00000001 
              40010000 
              00000000 
              00000000 
              00000000 
              00000000 
              00000400 
              00000200 
              00000000 
Command Line: --debug --xref --diag_suppress=9931 --cpu=Cortex-M0+ --apcs=inter
work --depend=.\objects\startup_stm32f091.d -o.\objects\startup_stm32f091.o -ID
:\Keli\Arm\Packs\Geehy\APM32F0xx_DFP\1.0.9\Device\Device\Geehy\APM32F0xx\Includ
e --predefine="__UVISION_VERSION SETA 539" --predefine="APM32F091XC SETA 1" --l
ist=.\listings\startup_stm32f091.lst Start\startup_stm32f091.s



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

STACK 00000000

Symbol: STACK
   Definitions
      At line 37 in file Start\startup_stm32f091.s
   Uses
      None
Comment: STACK unused
Stack_Mem 00000000

Symbol: Stack_Mem
   Definitions
      At line 38 in file Start\startup_stm32f091.s
   Uses
      At line 272 in file Start\startup_stm32f091.s
      At line 274 in file Start\startup_stm32f091.s

__initial_sp 00000400

Symbol: __initial_sp
   Definitions
      At line 39 in file Start\startup_stm32f091.s
   Uses
      At line 63 in file Start\startup_stm32f091.s
      At line 127 in file Start\startup_stm32f091.s

3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

HEAP 00000000

Symbol: HEAP
   Definitions
      At line 48 in file Start\startup_stm32f091.s
   Uses
      None
Comment: HEAP unused
Heap_Mem 00000000

Symbol: Heap_Mem
   Definitions
      At line 50 in file Start\startup_stm32f091.s
   Uses
      At line 271 in file Start\startup_stm32f091.s
      At line 273 in file Start\startup_stm32f091.s

__heap_base 00000000

Symbol: __heap_base
   Definitions
      At line 49 in file Start\startup_stm32f091.s
   Uses
      None
Comment: __heap_base unused
__heap_limit 00000200

Symbol: __heap_limit
   Definitions
      At line 51 in file Start\startup_stm32f091.s
   Uses
      None
Comment: __heap_limit unused
4 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

RESET 00000000

Symbol: RESET
   Definitions
      At line 58 in file Start\startup_stm32f091.s
   Uses
      None
Comment: RESET unused
__Vectors 00000000

Symbol: __Vectors
   Definitions
      At line 63 in file Start\startup_stm32f091.s
   Uses
      At line 59 in file Start\startup_stm32f091.s
      At line 115 in file Start\startup_stm32f091.s

__Vectors_End 000000BC

Symbol: __Vectors_End
   Definitions
      At line 113 in file Start\startup_stm32f091.s
   Uses
      At line 60 in file Start\startup_stm32f091.s
      At line 115 in file Start\startup_stm32f091.s

3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

.text 00000000

Symbol: .text
   Definitions
      At line 117 in file Start\startup_stm32f091.s
   Uses
      None
Comment: .text unused
ADC1_COMP_IRQHandler 00000030

Symbol: ADC1_COMP_IRQHandler
   Definitions
      At line 229 in file Start\startup_stm32f091.s
   Uses
      At line 93 in file Start\startup_stm32f091.s
      At line 196 in file Start\startup_stm32f091.s

ApplicationStart 0000001E

Symbol: ApplicationStart
   Definitions
      At line 151 in file Start\startup_stm32f091.s
   Uses
      At line 138 in file Start\startup_stm32f091.s
Comment: ApplicationStart used once
CEC_CAN_IRQHandler 00000030

Symbol: CEC_CAN_IRQHandler
   Definitions
      At line 247 in file Start\startup_stm32f091.s
   Uses
      At line 111 in file Start\startup_stm32f091.s
      At line 214 in file Start\startup_stm32f091.s

DMA1_Ch1_IRQHandler 00000030

Symbol: DMA1_Ch1_IRQHandler
   Definitions
      At line 226 in file Start\startup_stm32f091.s
   Uses
      At line 90 in file Start\startup_stm32f091.s
      At line 193 in file Start\startup_stm32f091.s

DMA1_Ch2_3_DMA2_Ch1_2_IRQHandler 00000030

Symbol: DMA1_Ch2_3_DMA2_Ch1_2_IRQHandler
   Definitions
      At line 227 in file Start\startup_stm32f091.s
   Uses
      At line 91 in file Start\startup_stm32f091.s
      At line 194 in file Start\startup_stm32f091.s

DMA1_Ch4_7_DMA2_Ch3_5_IRQHandler 00000030

Symbol: DMA1_Ch4_7_DMA2_Ch3_5_IRQHandler
   Definitions
      At line 228 in file Start\startup_stm32f091.s
   Uses
      At line 92 in file Start\startup_stm32f091.s



ARM Macro Assembler    Page 2 Alphabetic symbol ordering
Relocatable symbols

      At line 195 in file Start\startup_stm32f091.s

Default_Handler 00000030

Symbol: Default_Handler
   Definitions
      At line 182 in file Start\startup_stm32f091.s
   Uses
      None
Comment: Default_Handler unused
EXTI0_1_IRQHandler 00000030

Symbol: EXTI0_1_IRQHandler
   Definitions
      At line 222 in file Start\startup_stm32f091.s
   Uses
      At line 86 in file Start\startup_stm32f091.s
      At line 189 in file Start\startup_stm32f091.s

EXTI2_3_IRQHandler 00000030

Symbol: EXTI2_3_IRQHandler
   Definitions
      At line 223 in file Start\startup_stm32f091.s
   Uses
      At line 87 in file Start\startup_stm32f091.s
      At line 190 in file Start\startup_stm32f091.s

EXTI4_15_IRQHandler 00000030

Symbol: EXTI4_15_IRQHandler
   Definitions
      At line 224 in file Start\startup_stm32f091.s
   Uses
      At line 88 in file Start\startup_stm32f091.s
      At line 191 in file Start\startup_stm32f091.s

FLASH_IRQHandler 00000030

Symbol: FLASH_IRQHandler
   Definitions
      At line 220 in file Start\startup_stm32f091.s
   Uses
      At line 84 in file Start\startup_stm32f091.s
      At line 187 in file Start\startup_stm32f091.s

HardFault_Handler 00000028

Symbol: HardFault_Handler
   Definitions
      At line 165 in file Start\startup_stm32f091.s
   Uses
      At line 66 in file Start\startup_stm32f091.s
      At line 166 in file Start\startup_stm32f091.s

I2C1_IRQHandler 00000030

Symbol: I2C1_IRQHandler
   Definitions



ARM Macro Assembler    Page 3 Alphabetic symbol ordering
Relocatable symbols

      At line 240 in file Start\startup_stm32f091.s
   Uses
      At line 104 in file Start\startup_stm32f091.s
      At line 207 in file Start\startup_stm32f091.s

I2C2_IRQHandler 00000030

Symbol: I2C2_IRQHandler
   Definitions
      At line 241 in file Start\startup_stm32f091.s
   Uses
      At line 105 in file Start\startup_stm32f091.s
      At line 208 in file Start\startup_stm32f091.s

NMI_Handler 00000026

Symbol: NMI_Handler
   Definitions
      At line 160 in file Start\startup_stm32f091.s
   Uses
      At line 65 in file Start\startup_stm32f091.s
      At line 161 in file Start\startup_stm32f091.s

PVD_VDDIO2_IRQHandler 00000030

Symbol: PVD_VDDIO2_IRQHandler
   Definitions
      At line 218 in file Start\startup_stm32f091.s
   Uses
      At line 82 in file Start\startup_stm32f091.s
      At line 185 in file Start\startup_stm32f091.s

PendSV_Handler 0000002C

Symbol: PendSV_Handler
   Definitions
      At line 173 in file Start\startup_stm32f091.s
   Uses
      At line 77 in file Start\startup_stm32f091.s
      At line 174 in file Start\startup_stm32f091.s

RCC_CRS_IRQHandler 00000030

Symbol: RCC_CRS_IRQHandler
   Definitions
      At line 221 in file Start\startup_stm32f091.s
   Uses
      At line 85 in file Start\startup_stm32f091.s
      At line 188 in file Start\startup_stm32f091.s

RTC_IRQHandler 00000030

Symbol: RTC_IRQHandler
   Definitions
      At line 219 in file Start\startup_stm32f091.s
   Uses
      At line 83 in file Start\startup_stm32f091.s
      At line 186 in file Start\startup_stm32f091.s




ARM Macro Assembler    Page 4 Alphabetic symbol ordering
Relocatable symbols

Reset_Handler 00000000

Symbol: Reset_Handler
   Definitions
      At line 120 in file Start\startup_stm32f091.s
   Uses
      At line 64 in file Start\startup_stm32f091.s
      At line 121 in file Start\startup_stm32f091.s

SPI1_IRQHandler 00000030

Symbol: SPI1_IRQHandler
   Definitions
      At line 242 in file Start\startup_stm32f091.s
   Uses
      At line 106 in file Start\startup_stm32f091.s
      At line 209 in file Start\startup_stm32f091.s

SPI2_IRQHandler 00000030

Symbol: SPI2_IRQHandler
   Definitions
      At line 243 in file Start\startup_stm32f091.s
   Uses
      At line 107 in file Start\startup_stm32f091.s
      At line 210 in file Start\startup_stm32f091.s

SVC_Handler 0000002A

Symbol: SVC_Handler
   Definitions
      At line 169 in file Start\startup_stm32f091.s
   Uses
      At line 74 in file Start\startup_stm32f091.s
      At line 170 in file Start\startup_stm32f091.s

SysTick_Handler 0000002E

Symbol: SysTick_Handler
   Definitions
      At line 177 in file Start\startup_stm32f091.s
   Uses
      At line 78 in file Start\startup_stm32f091.s
      At line 178 in file Start\startup_stm32f091.s

TIM14_IRQHandler 00000030

Symbol: TIM14_IRQHandler
   Definitions
      At line 236 in file Start\startup_stm32f091.s
   Uses
      At line 100 in file Start\startup_stm32f091.s
      At line 203 in file Start\startup_stm32f091.s

TIM15_IRQHandler 00000030

Symbol: TIM15_IRQHandler
   Definitions
      At line 237 in file Start\startup_stm32f091.s



ARM Macro Assembler    Page 5 Alphabetic symbol ordering
Relocatable symbols

   Uses
      At line 101 in file Start\startup_stm32f091.s
      At line 204 in file Start\startup_stm32f091.s

TIM16_IRQHandler 00000030

Symbol: TIM16_IRQHandler
   Definitions
      At line 238 in file Start\startup_stm32f091.s
   Uses
      At line 102 in file Start\startup_stm32f091.s
      At line 205 in file Start\startup_stm32f091.s

TIM17_IRQHandler 00000030

Symbol: TIM17_IRQHandler
   Definitions
      At line 239 in file Start\startup_stm32f091.s
   Uses
      At line 103 in file Start\startup_stm32f091.s
      At line 206 in file Start\startup_stm32f091.s

TIM1_BRK_UP_TRG_COM_IRQHandler 00000030

Symbol: TIM1_BRK_UP_TRG_COM_IRQHandler
   Definitions
      At line 230 in file Start\startup_stm32f091.s
   Uses
      At line 94 in file Start\startup_stm32f091.s
      At line 197 in file Start\startup_stm32f091.s

TIM1_CC_IRQHandler 00000030

Symbol: TIM1_CC_IRQHandler
   Definitions
      At line 231 in file Start\startup_stm32f091.s
   Uses
      At line 95 in file Start\startup_stm32f091.s
      At line 198 in file Start\startup_stm32f091.s

TIM2_IRQHandler 00000030

Symbol: TIM2_IRQHandler
   Definitions
      At line 232 in file Start\startup_stm32f091.s
   Uses
      At line 96 in file Start\startup_stm32f091.s
      At line 199 in file Start\startup_stm32f091.s

TIM3_IRQHandler 00000030

Symbol: TIM3_IRQHandler
   Definitions
      At line 233 in file Start\startup_stm32f091.s
   Uses
      At line 97 in file Start\startup_stm32f091.s
      At line 200 in file Start\startup_stm32f091.s

TIM6_DAC_IRQHandler 00000030



ARM Macro Assembler    Page 6 Alphabetic symbol ordering
Relocatable symbols


Symbol: TIM6_DAC_IRQHandler
   Definitions
      At line 234 in file Start\startup_stm32f091.s
   Uses
      At line 98 in file Start\startup_stm32f091.s
      At line 201 in file Start\startup_stm32f091.s

TIM7_IRQHandler 00000030

Symbol: TIM7_IRQHandler
   Definitions
      At line 235 in file Start\startup_stm32f091.s
   Uses
      At line 99 in file Start\startup_stm32f091.s
      At line 202 in file Start\startup_stm32f091.s

TSC_IRQHandler 00000030

Symbol: TSC_IRQHandler
   Definitions
      At line 225 in file Start\startup_stm32f091.s
   Uses
      At line 89 in file Start\startup_stm32f091.s
      At line 192 in file Start\startup_stm32f091.s

USART1_IRQHandler 00000030

Symbol: USART1_IRQHandler
   Definitions
      At line 244 in file Start\startup_stm32f091.s
   Uses
      At line 108 in file Start\startup_stm32f091.s
      At line 211 in file Start\startup_stm32f091.s

USART2_IRQHandler 00000030

Symbol: USART2_IRQHandler
   Definitions
      At line 245 in file Start\startup_stm32f091.s
   Uses
      At line 109 in file Start\startup_stm32f091.s
      At line 212 in file Start\startup_stm32f091.s

USART3_8_IRQHandler 00000030

Symbol: USART3_8_IRQHandler
   Definitions
      At line 246 in file Start\startup_stm32f091.s
   Uses
      At line 110 in file Start\startup_stm32f091.s
      At line 213 in file Start\startup_stm32f091.s

WWDG_IRQHandler 00000030

Symbol: WWDG_IRQHandler
   Definitions
      At line 217 in file Start\startup_stm32f091.s
   Uses



ARM Macro Assembler    Page 7 Alphabetic symbol ordering
Relocatable symbols

      At line 81 in file Start\startup_stm32f091.s
      At line 184 in file Start\startup_stm32f091.s

__user_initial_stackheap 00000034

Symbol: __user_initial_stackheap
   Definitions
      At line 269 in file Start\startup_stm32f091.s
   Uses
      At line 267 in file Start\startup_stm32f091.s
Comment: __user_initial_stackheap used once
41 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Absolute symbols

Heap_Size 00000200

Symbol: Heap_Size
   Definitions
      At line 46 in file Start\startup_stm32f091.s
   Uses
      At line 50 in file Start\startup_stm32f091.s
      At line 273 in file Start\startup_stm32f091.s

Stack_Size 00000400

Symbol: Stack_Size
   Definitions
      At line 35 in file Start\startup_stm32f091.s
   Uses
      At line 38 in file Start\startup_stm32f091.s
      At line 272 in file Start\startup_stm32f091.s

__Vectors_Size 000000BC

Symbol: __Vectors_Size
   Definitions
      At line 115 in file Start\startup_stm32f091.s
   Uses
      At line 61 in file Start\startup_stm32f091.s
Comment: __Vectors_Size used once
3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
External symbols

SystemInit 00000000

Symbol: SystemInit
   Definitions
      At line 123 in file Start\startup_stm32f091.s
   Uses
      At line 152 in file Start\startup_stm32f091.s
Comment: SystemInit used once
__main 00000000

Symbol: __main
   Definitions
      At line 122 in file Start\startup_stm32f091.s
   Uses
      At line 154 in file Start\startup_stm32f091.s
Comment: __main used once
__use_two_region_memory 00000000

Symbol: __use_two_region_memory
   Definitions
      At line 266 in file Start\startup_stm32f091.s
   Uses
      None
Comment: __use_two_region_memory unused
3 symbols
393 symbols in table
